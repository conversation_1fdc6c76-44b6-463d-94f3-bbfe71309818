<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单位问卷列表</title>
    <link href="${springMacroRequestContext.getContextPath()}/enterprise/bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="${springMacroRequestContext.getContextPath()}/enterprise/fontawesome/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        .header {
            background: #1e3a8a;
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .breadcrumb-custom {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 15px 20px;
            margin-bottom: 20px;
        }
        .enterprise-info-card {
            border: 1px solid #1e3a8a;
            background: white;
            color: #1e3a8a;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .enterprise-name {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .survey-list-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .survey-list-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            border-radius: 10px 10px 0 0;
        }
        .survey-list-body {
            padding: 0;
        }
        .survey-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f1f3f4;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s;
        }
        .survey-item:last-child {
            border-bottom: none;
        }
        .survey-item:hover {
            background-color: #f8f9fa;
        }
        .survey-name {
            flex: 1;
            font-size: 1em;
            color: #495057;
            margin: 0;
            padding-right: 15px;
        }
        .btn-view {
            background: #1e3a8a;
            color: white;
            border: none;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 0.9em;
            transition: all 0.2s;
            white-space: nowrap;
        }
        .btn-view:hover {
            background: #1e40af;
            color: white;
            transform: scale(1.05);
        }
        .no-data {
            text-align: center;
            padding: 50px 20px;
            color: #6c757d;
        }
        .loading {
            text-align: center;
            padding: 50px 20px;
            color: #6c757d;
        }
        .list-title {
            font-size: 1.1em;
            font-weight: bold;
            color: #495057;
            margin: 0;
        }
        .list-count {
            color: #6c757d;
            font-size: 0.9em;
        }

        .enterprise-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .detail-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .detail-icon {
            width: 16px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2><i class="fas fa-file-alt"></i> 单位问卷列表</h2>
                </div>
                <div class="col-md-4 text-right">
                    <a href="/enterprise/index" class="btn btn-outline-light">
                        <i class="fas fa-arrow-left"></i> 返回单位列表
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 面包屑导航 -->
        <div class="breadcrumb-custom">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="/enterprise/index">单位管理</a></li>
                    <li class="breadcrumb-item active" aria-current="page" id="enterpriseName">单位问卷</li>
                </ol>
            </nav>
        </div>

        <!-- 单位信息卡片 -->
        <div class="enterprise-info-card" id="enterpriseInfo">
            <div class="loading">
                <i class="fas fa-spinner fa-spin"></i> 正在加载单位信息...
            </div>
        </div>

        <!-- 问卷列表 -->
        <div class="survey-list-card">
            <div class="survey-list-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="list-title">问卷列表</h5>
                    <span class="list-count" id="surveyCount">共 0 份问卷</span>
                </div>
            </div>
            <div class="survey-list-body" id="surveyList">
                <div class="loading">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                    <p class="mt-3">正在加载问卷信息...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="${springMacroRequestContext.getContextPath()}/enterprise/jquery/jquery.min.js"></script>
    <script src="${springMacroRequestContext.getContextPath()}/enterprise/bootstrap/bootstrap.bundle.min.js"></script>
    <script>
        // 当前页面
        let enterpriseId = '';

        // 页面加载完成后初始化
        $(document).ready(function() {
            // 从URL参数获取单位ID
            const urlParams = new URLSearchParams(window.location.search);
            enterpriseId = urlParams.get('enterpriseId');

            if (!enterpriseId) {
                showError('缺少单位ID参数');
                return;
            }

            // 加载单位信息和问卷列表
            loadEnterpriseInfo();
            loadSurvey();
        });

        // 加载单位信息
        function loadEnterpriseInfo() {
            $.ajax({
                url: '/enterprise/detail/' + enterpriseId,
                type: 'GET',
                success: function(response) {
                    if (response.code === 200) {
                        renderEnterpriseInfo(response.data);
                    } else {
                        $('#enterpriseInfo').html(`
                            <div class="text-center text-danger">
                                <i class="fas fa-exclamation-triangle"></i> 加载单位信息失败：` + response.msg + `
                            </div>
                        `);
                    }
                },
                error: function() {
                    $('#enterpriseInfo').html(`
                        <div class="text-center text-danger">
                            <i class="fas fa-exclamation-triangle"></i> 网络错误，请稍后重试
                        </div>
                    `);
                }
            });
        }

        // 渲染单位信息
        function renderEnterpriseInfo(enterprise) {
            $('#enterpriseName').text(enterprise.name + ' - 问卷列表');

            const html = `
                <div class="enterprise-name">` + enterprise.name + `</div>
                <div class="enterprise-details">
                    <div class="detail-item">
                        <i class="fas fa-industry detail-icon"></i>
                        <span>：` + (enterprise.type || '未设置') + `</span>
                    </div>
                </div>
            `;

            $('#enterpriseInfo').html(html);
        }

        // 加载问卷列表 - 使用示例数据格式
        function loadSurvey() {
            // 这里应该是实际的AJAX请求，现在使用示例数据来演示
            const exampleResponse = {
                "msg": "操作成功",
                "code": 200,
                "data": [
                    {
                        "jmReportId": "",
                        "enterpriseId": "7351157672940404736",
                        "enterpriseName": "上海核工程研究设计院股份有限公司",
                        "reportName": "上海核领域人才队伍建设课题调查问卷（用人单位版）"
                    },
                    {
                        "jmReportId": "",
                        "enterpriseId": "7351157672940404736",
                        "enterpriseName": "上海核工程研究设计院股份有限公司",
                        "reportName": "上海核领域人才队伍建设课题调查问卷（教育单位版）"
                    }
                ]
            };

            // 模拟网络延迟
            setTimeout(() => {
                if (exampleResponse.code === 200) {
                    renderSurveyList(exampleResponse.data);
                } else {
                    showError('加载问卷列表失败：' + exampleResponse.msg);
                }
            }, 1000);

            // 实际使用时，请替换为以下代码：
            /*
            $.ajax({
                url: '/enterprise/survey/' + enterpriseId,
                type: 'GET',
                success: function(response) {
                    if (response.code === 200) {
                        renderSurveyList(response.data);
                    } else {
                        showError('加载问卷列表失败：' + response.msg);
                    }
                },
                error: function() {
                    showError('网络错误，请稍后重试');
                }
            });
            */
        }

        // 渲染问卷列表
        function renderSurveyList(surveys) {
            const listContainer = $('#surveyList');
            const countContainer = $('#surveyCount');

            if (!surveys || surveys.length === 0) {
                listContainer.html(`
                    <div class="no-data">
                        <i class="fas fa-inbox fa-3x"></i>
                        <p class="mt-3">暂无问卷信息</p>
                    </div>
                `);
                countContainer.text('共 0 份问卷');
                return;
            }

            let html = '';
            surveys.forEach(function(survey, index) {
                html += `
                    <div class="survey-item">
                        <div class="survey-name">` + survey.reportName + `</div>
                        <button class="btn btn-view" onclick="viewSurvey('` + (survey.jmReportId || survey.reportName) + `')">
                            <i class="fas fa-eye"></i> 查看
                        </button>
                    </div>
                `;
            });

            listContainer.html(html);
            countContainer.text('共 ' + surveys.length + ' 份问卷');
        }

        // 查看问卷
        function viewSurvey(surveyId) {
            // 根据实际需求修改跳转URL
            if (surveyId) {
                window.open('/jmreport/view/' + surveyId, '_blank');
            } else {
                alert('问卷ID为空，无法查看');
            }
        }

        // 显示错误信息
        function showError(message) {
            $('#surveyList').html(`
                <div class="no-data">
                    <i class="fas fa-exclamation-triangle fa-3x text-danger"></i>
                    <p class="mt-3 text-danger">` + message + `</p>
                </div>
            `);
        }
    </script>
</body>
</html>