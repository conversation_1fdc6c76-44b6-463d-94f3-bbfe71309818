<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单位信息管理</title>
    <link href="${springMacroRequestContext.getContextPath()}/enterprise/bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="${springMacroRequestContext.getContextPath()}/enterprise/fontawesome/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        .header {
            background: #1e3a8a;
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .search-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .enterprise-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .enterprise-table .table {
            margin-bottom: 0;
        }
        .enterprise-table .table thead th {
            background: #1e3a8a;
            color: white;
            border: none;
            font-weight: 600;
            padding: 15px;
            text-align: center;
        }
        .enterprise-table .table tbody tr {
            transition: background-color 0.2s;
        }
        .enterprise-table .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        .enterprise-table .table tbody td {
            padding: 15px;
            vertical-align: middle;
            border-top: 1px solid #e9ecef;
            text-align: center;
        }
        .enterprise-name {
            font-weight: bold;
            color: #495057;
            font-size: 1.1em;
        }
        .enterprise-id {
            color: #6c757d;
            font-family: 'Courier New', monospace;
        }
        .enterprise-type {
            color: #495057;
        }
        .enterprise-sort {
            color: #495057;
            font-weight: 500;
        }
        .btn-view-reports {
            background: #1e3a8a;
            border: none;
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 0.9em;
            transition: all 0.2s;
        }
        .btn-view-reports:hover {
            background: #1e40af;
            color: white;
            transform: scale(1.05);
        }
        .pagination-wrapper {
            display: flex;
            justify-content: center;
            margin-top: 30px;
        }
        .loading {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }
        .no-data {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }
        .search-btn {
            background: #1e3a8a;
            border: none;
            color: white;
            padding: 10px 25px;
            border-radius: 25px;
            transition: all 0.2s;
        }
        .search-btn:hover {
            background: #1e40af;
            color: white;
            transform: scale(1.05);
        }
        .reset-btn {
            background: #6c757d;
            border: none;
            color: white;
            padding: 10px 25px;
            border-radius: 25px;
            margin-left: 10px;
            transition: all 0.2s;
        }
        .reset-btn:hover {
            background: #5a6268;
            color: white;
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container-lg">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2><i class="fas fa-building"></i> 单位信息管理</h2>
                </div>
<#--                <div class="col-md-6 text-right">-->
<#--                    <a href="/jmreport/list" class="btn btn-outline-light">-->
<#--                        <i class="fas fa-chart-bar"></i> 报表工作台-->
<#--                    </a>-->
<#--                    <a href="/drag/list" class="btn btn-outline-light ml-2">-->
<#--                        <i class="fas fa-tachometer-alt"></i> 仪表盘工作台-->
<#--                    </a>-->
<#--                </div>-->
            </div>
        </div>
    </div>

    <div class="container-lg">
        <!-- 搜索区域 -->
        <div class="search-card">
            <form id="searchForm">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="enterpriseName">单位名称</label>
                            <input type="text" class="form-control" id="enterpriseName" name="name" placeholder="请输入单位名称">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="enterpriseType">单位类型</label>
                            <select class="form-control" id="enterpriseType" name="type">
                                <option value="">请选择类型</option>
                                <option value="研发设计">研发设计</option>
                                <option value="装备制造">装备制造</option>
                                <option value="运行维护">运行维护</option>
                                <option value="高校">高校</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <div class="form-group">
                            <button type="submit" class="btn search-btn">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                            <button type="button" class="btn reset-btn" onclick="resetForm()">
                                <i class="fas fa-redo"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- 单位列表区域 -->
        <div id="enterpriseList">
            <div class="loading">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p class="mt-3">正在加载单位信息...</p>
            </div>
        </div>

        <!-- 分页区域 -->
        <div class="pagination-wrapper">
            <nav aria-label="单位列表分页">
                <ul class="pagination" id="pagination">
                    <!-- 分页内容将通过JavaScript动态生成 -->
                </ul>
            </nav>
        </div>
    </div>

    <!-- 单位报表模态框 -->
    <div class="modal fade" id="reportsModal" tabindex="-1" role="dialog" aria-labelledby="reportsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="reportsModalLabel">
                        <i class="fas fa-file-alt"></i> 单位报表列表
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="reportsModalBody">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <p class="mt-3">正在加载报表信息...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="${springMacroRequestContext.getContextPath()}/enterprise/jquery/jquery.min.js"></script>
    <script src="${springMacroRequestContext.getContextPath()}/enterprise/bootstrap/bootstrap.bundle.min.js"></script>
    <script>
        // 当前页面
        let pageNum = 1;
        // 每页显示数量
        let pageSize = 10;
        // 总记录数
        let total = 0;

        // 页面加载完成后初始化
        $(document).ready(function() {
            loadEnterprises(1);
            
            // 搜索表单提交
            $('#searchForm').on('submit', function(e) {
                e.preventDefault();
                loadEnterprises(1);
            });
        });

        // 加载单位列表
        function loadEnterprises(page) {
            pageNum = page;
            const formData = $('#searchForm').serialize();
            
            $.ajax({
                url: '/enterprise/list',
                type: 'GET',
                dataType: 'json',
                data: formData + '&pageNum=' + page + '&pageSize=' + pageSize,
                success: function(response) {
                    if (response.code === 200) {
                        total = response.total;
                        renderEnterpriseList(response.rows);
                        renderPagination();
                    } else {
                        showError('加载单位信息失败：' + response.msg);
                    }
                },
                error: function() {
                    showError('网络错误，请稍后重试');
                }
            });
        }

        // 渲染单位列表
        function renderEnterpriseList(enterprises) {
            const listContainer = $('#enterpriseList');

            if (!enterprises || enterprises.length === 0) {
                listContainer.html(`
                    <div class="no-data">
                        <i class="fas fa-inbox fa-3x"></i>
                        <p class="mt-3">暂无单位信息</p>
                    </div>
                `);
                return;
            }

            let html = `
                <div class="enterprise-table">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>单位名称</th>
                                <th>单位ID</th>
                                <th>单位类型</th>
                                <th>排序</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            enterprises.forEach(function(enterprise) {
                html += `
                    <tr>
                        <td>
                            <div class="enterprise-name">` + enterprise.name + `</div>
                        </td>
                        <td>
                            <div class="enterprise-id">` + enterprise.id + `</div>
                        </td>
                        <td>
                            <div class="enterprise-type">` + (enterprise.type || '未设置') + `</div>
                        </td>
                        <td>
                            <div class="enterprise-sort">` + (enterprise.sort || 0) + `</div>
                        </td>
                        <td>
                            <button class="btn btn-view-reports" onclick="viewEnterpriseReports('` + enterprise.id + `', '` + enterprise.name + `')">
                                <i class="fas fa-eye"></i> 查看报表
                            </button>
                        </td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            listContainer.html(html);
        }

        // 渲染分页
        function renderPagination() {
            let pages = Math.ceil(total / pageSize);
            const pagination = $('#pagination');

            // 如果总页数小于等于1，不显示分页
            if (pages <= 1) {
                pagination.empty();
                return;
            }

            let html = '';

            // 上一页
            if (pageNum > 1) {
                html += `<li class="page-item"><a class="page-link" href="javascript:void(0);" onclick="loadEnterprises(` + (pageNum - 1) + ` )">上一页</a></li>`;
            }

            // 页码
            const startPage = Math.max(1, pageNum - 2);
            const endPage = Math.min(pages, pageNum + 2);

            if (startPage > 1) {
                html += `<li class="page-item"><a class="page-link" href="javascript:void(0);" onclick="loadEnterprises(1)">1</a></li>`;
                if (startPage > 2) {
                    html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                if (i === pageNum) {
                    html += `<li class="page-item active"><span class="page-link">`+i+`</span></li>`;
                } else {
                    html += `<li class="page-item"><a class="page-link" href="javascript:void(0);" onclick="loadEnterprises(`+i+`)">`+i+`</a></li>`;
                }
            }

            if (endPage < pages) {
                if (endPage < pages - 1) {
                    html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
                html += `<li class="page-item"><a class="page-link" href="javascript:void(0);" onclick="loadEnterprises(` + pages + `)">` + pages + `</a></li>`;
            }

            // 下一页
            if (pageNum < pages) {
                html += `<li class="page-item"><a class="page-link" href="javascript:void(0);" onclick="loadEnterprises(` + (pageNum + 1) + `)">下一页</a></li>`;
            }

            pagination.html(html);
        }

        // 查看单位报表
        function viewEnterpriseReports(enterpriseId, enterpriseName) {
            // 直接跳转到单位报表页面
            window.location.href = '/enterprise/reports?enterpriseId=' + enterpriseId;
        }

        // 渲染报表列表
        function renderReportsList(reports) {
            const modalBody = $('#reportsModalBody');
            
            if (!reports || reports.length === 0) {
                modalBody.html(`
                    <div class="text-center text-muted">
                        <i class="fas fa-inbox fa-3x"></i>
                        <p class="mt-3">该单位暂无填报报表</p>
                    </div>
                `);
                return;
            }

            let html = '<div class="list-group">';
            reports.forEach(function(report) {
                html += `
                    <div class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">` + report.name + `</h6>
                            <small class="text-muted">` + report.createTime + `</small>
                        </div>
                        <p class="mb-1 text-muted">` + (report.description || '暂无描述') + `</p>
                        <div class="mt-2">
                            <a href="/jmreport/view/` + report.id + `" target="_blank" class="btn btn-sm btn-primary">
                                <i class="fas fa-eye"></i> 查看报表
                            </a>
                            <a href="/jmreport/exportExcel/` + report.id + `" class="btn btn-sm btn-success ml-2">
                                <i class="fas fa-download"></i> 导出Excel
                            </a>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            
            modalBody.html(html);
        }

        // 重置表单
        function resetForm() {
            $('#searchForm')[0].reset();
            pageNum = 1;
            loadEnterprises(1);
        }

        // 显示错误信息
        function showError(message) {
            $('#enterpriseList').html(
                '<div class="text-center text-danger">'
                +'    <i class="fas fa-exclamation-triangle fa-3x"></i>'
                +'    <p class="mt-3">' + message + '</p>'
                +'</div>');
        }
    </script>
</body>
</html>
