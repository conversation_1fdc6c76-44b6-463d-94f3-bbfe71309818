package com.jeecg.modules.enterprise.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 单位信息实体类
 * 
 * <AUTHOR>
 * @date 2024-12-11
 */
@Data
@TableName("单位信息表")
public class Enterprise {

    /**
     * 企业ID
     */
    @TableId("主键")
    private Long id;

    /**
     * 单位名称
     */
    @TableField("单位名称")
    private String name;

    /**
     * 单位类型
     */
    @TableField("单位类型")
    private String type;

    /**
     * 单位类型
     */
    @TableField("排序")
    private Integer sort;

}
