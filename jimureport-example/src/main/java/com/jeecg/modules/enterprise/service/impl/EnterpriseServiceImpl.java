package com.jeecg.modules.enterprise.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeecg.modules.enterprise.domain.Enterprise;
import com.jeecg.modules.enterprise.domain.vo.EnterpriseSurveyVo;
import com.jeecg.modules.enterprise.mapper.EnterpriseMapper;
import com.jeecg.modules.enterprise.mapper.SurveyMapper;
import com.jeecg.modules.enterprise.service.IEnterpriseService;
import com.jeecg.modules.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 企业信息服务实现类
 * 
 * <AUTHOR>
 * @date 2024-12-11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EnterpriseServiceImpl extends ServiceImpl<EnterpriseMapper, Enterprise> implements IEnterpriseService {

    private final SurveyMapper surveyMapper;

    /** 高层次人才基本情况表报表模板ID */
    private final String rcInfoJmReportId = "1114784676226355200";
    private final String rcInfoJmReportName = "高层次人才基本情况表";
    private final String rcFormYrJmReportId = "";
    private final String rcFormYrJmReportName = "上海核领域人才队伍建设课题调查问卷（用人单位版）";
    private final String rcFormJyJmReportId = "";
    private final String rcFormJyJmReportName = "上海核领域人才队伍建设课题调查问卷（教育单位版）";

    @Override
    public List<Enterprise> getEnterpriseList(Enterprise query) {
        List<Enterprise> list = this.lambdaQuery()
                .eq(StringUtils.hasText(query.getType()), Enterprise::getType, query.getType())
                .like(StringUtils.hasText(query.getName()), Enterprise::getName, query.getName())
                .orderByAsc(Enterprise::getSort)
                .list();
        return list;
    }

    @Override
    public Enterprise getEnterpriseById(Long enterpriseId) {
        return getById(enterpriseId);
    }

    @Override
    public List<EnterpriseSurveyVo> listEnterpriseSurvey(Long enterpriseId) {
        Enterprise enterprise = getById(enterpriseId);
        if(enterprise == null){
            return Collections.emptyList();
        }
        // 模拟企业报表数据
        List<EnterpriseSurveyVo> reports = new ArrayList<>();
        // 高层次人才基本情况表填报了
        if(surveyMapper.hasRcInfoReport(enterpriseId)){
            EnterpriseSurveyVo vo = new EnterpriseSurveyVo()
            .setJmReportId(rcInfoJmReportId)
            .setEnterpriseId(enterpriseId)
            .setEnterpriseName(enterprise.getName())
            .setReportName(rcInfoJmReportName);
            reports.add(vo);
        }
        if(surveyMapper.hasRcFormReport(enterpriseId,"用人单位")){
            EnterpriseSurveyVo vo = new EnterpriseSurveyVo()
                    .setJmReportId(rcFormYrJmReportId)
                    .setEnterpriseId(enterpriseId)
                    .setEnterpriseName(enterprise.getName())
                    .setReportName(rcFormYrJmReportName);
            reports.add(vo);

        }
        if(surveyMapper.hasRcFormReport(enterpriseId,"教育单位")){
            EnterpriseSurveyVo vo = new EnterpriseSurveyVo()
                    .setJmReportId(rcFormJyJmReportId)
                    .setEnterpriseId(enterpriseId)
                    .setEnterpriseName(enterprise.getName())
                    .setReportName(rcFormJyJmReportName);
            reports.add(vo);
        }
        return reports;
    }

}
