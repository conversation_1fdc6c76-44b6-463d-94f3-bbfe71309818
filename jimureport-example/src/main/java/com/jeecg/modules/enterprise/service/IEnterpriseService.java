package com.jeecg.modules.enterprise.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jeecg.modules.enterprise.domain.Enterprise;
import com.jeecg.modules.enterprise.domain.vo.EnterpriseSurveyVo;

import java.util.List;
import java.util.Map;

/**
 * 企业信息服务接口
 * 
 * <AUTHOR>
 * @date 2024-12-11
 */
public interface IEnterpriseService extends IService<Enterprise> {

    /**
     * 获取企业信息列表
     * 
     * @param query 查询参数
     * @return 企业信息列表
     */
    List<Enterprise> getEnterpriseList(Enterprise query);

    /**
     * 根据ID获取企业信息
     * 
     * @param enterpriseId 企业ID
     * @return 企业信息
     */
    Enterprise getEnterpriseById(Long enterpriseId);

    /**
     * 获取企业报表列表
     * 
     * @param enterpriseId 企业ID
     * @return 企业报表列表
     */
    List<EnterpriseSurveyVo> listEnterpriseSurvey(Long enterpriseId);

}
