package com.jeecg.modules.enterprise.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业报表实体类
 * 
 * <AUTHOR>
 * @date 2024-12-11
 */
@Data
@Accessors(chain = true)
public class EnterpriseSurveyVo {

    /**
     * 报表ID
     */
    private String jmReportId;

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 报表名称
     */
    private String reportName;


//    /**
//     * 报表URL
//     */
//    private String reportUrl;
//
//    /**
//     * 预览URL
//     */
//    private String previewUrl;
//
//    /**
//     * 下载URL
//     */
//    private String downloadUrl;

//    /**
//     * 填报开始时间
//     */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date startTime;
//
//    /**
//     * 填报结束时间
//     */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date endTime;
//
//    /**
//     * 提交时间
//     */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date submitTime;
//
//    /**
//     * 审核时间
//     */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date auditTime;
//
//    /**
//     * 审核人
//     */
//    private String auditBy;
//
//    /**
//     * 审核意见
//     */
//    private String auditComment;
//
//    /**
//     * 填报人
//     */
//    private String reportBy;
//
//    /**
//     * 填报人姓名
//     */
//    private String reportByName;
//
//    /**
//     * 创建人
//     */
//    private String createBy;
//
//    /**
//     * 创建时间
//     */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date createTime;
//
//    /**
//     * 更新人
//     */
//    private String updateBy;
//
//    /**
//     * 更新时间
//     */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date updateTime;
//
//    /**
//     * 备注
//     */
//    private String remark;
//
//    /**
//     * 删除标志（0：正常，1：删除）
//     */
//    private Integer delFlag;
//
//    /**
//     * 版本号
//     */
//    private Integer version;
//
//    /**
//     * 排序号
//     */
//    private Integer sortOrder;
//
//    /**
//     * 是否必填（0：否，1：是）
//     */
//    private Integer required;
//
//    /**
//     * 是否公开（0：否，1：是）
//     */
//    private Integer isPublic;
//
//    /**
//     * 标签
//     */
//    private String tags;
}
