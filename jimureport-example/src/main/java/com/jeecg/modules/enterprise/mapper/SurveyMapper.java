package com.jeecg.modules.enterprise.mapper;


import org.apache.ibatis.annotations.Select;


public interface SurveyMapper {

    /**
     * 高层次人才基本情况表是否填报
     * @param enterpriseId 单位ID
     * @return 结果
     */
    @Select("SELECT CASE WHEN EXISTS (select 1 from 高层次人才基本情况表 where 单位ID = #{enterpriseId} ) THEN 1 ELSE 0 END")
    boolean hasRcInfoReport(Long enterpriseId);


    /**
     * 核领域人才队伍建设调查问卷是否填报
     * @param enterpriseId 单位ID
     * @param type 类型
     * @return 结果
     */
    @Select("SELECT CASE WHEN EXISTS (select 1 from 核领域人才队伍建设调查问卷 where 单位ID = #{enterpriseId} and 类型 = #{type} ) THEN 1 ELSE 0 END")
    boolean hasRcFormReport(Long enterpriseId, String type);
}
