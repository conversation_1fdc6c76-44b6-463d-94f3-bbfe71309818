package com.jeecg.modules.enterprise.controller;

import com.jeecg.modules.enterprise.domain.AjaxResult;
import com.jeecg.modules.enterprise.domain.Enterprise;
import com.jeecg.modules.enterprise.domain.vo.EnterpriseSurveyVo;
import com.jeecg.modules.enterprise.service.IEnterpriseService;
import com.jeecg.modules.page.TableDataInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 企业信息管理控制器
 * 提供企业信息列表查询、搜索、分页等功能
 * 
 * <AUTHOR>
 * @date 2024-12-11
 */
@Slf4j
@Controller
@RequestMapping("/enterprise")
public class EnterpriseController extends BaseController{

    @Autowired
    private IEnterpriseService enterpriseService;

    /**
     * 企业信息管理主页面
     */
    @GetMapping("/index")
    public String index(Model model) {
        model.addAttribute("title", "企业信息");
        return "enterprise/list";
    }

    /**
     * 企业问卷列表页面
     */
    @GetMapping("/reports")
    public String reports(@RequestParam String enterpriseId, Model model) {
        model.addAttribute("enterpriseId", enterpriseId);
        model.addAttribute("title", "企业问卷");
        return "enterprise/reports";
    }

    /**
     * 获取企业信息列表（分页查询）
     *
     * @param query 查询参数
     * @return 分页结果
     */
    @GetMapping("/list")
    @ResponseBody
    public TableDataInfo getEnterpriseList(Enterprise query) {
        startPage();
        List<Enterprise> list = enterpriseService.getEnterpriseList(query);
        return getDataTable(list);
    }

    /**
     * 获取企业问卷列表
     * 
     * @param enterpriseId 企业ID
     * @return 业问卷列表
     */
    @GetMapping("/survey/{enterpriseId}")
    @ResponseBody
    public AjaxResult listEnterpriseSurvey(@PathVariable Long enterpriseId) {
        List<EnterpriseSurveyVo> vos = enterpriseService.listEnterpriseSurvey(enterpriseId);
        return success(vos);
    }

    /**
     * 获取企业详细信息
     *
     * @param enterpriseId 企业ID
     * @return 企业详细信息
     */
    @GetMapping("/detail/{enterpriseId}")
    @ResponseBody
    public AjaxResult getEnterpriseDetail(@PathVariable Long enterpriseId) {
        Enterprise enterprise = enterpriseService.getEnterpriseById(enterpriseId);
        return success(enterprise);
    }

}
