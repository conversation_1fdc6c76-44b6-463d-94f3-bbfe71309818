package com.jeecg.modules.jmreport.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 积木报表-设置默认首页跳转
 */
@Controller
public class IndexController {
    private Logger logger = LoggerFactory.getLogger(IndexController.class);

    @GetMapping("/")
    public void index(Model model, HttpServletResponse response) throws IOException {
        model.addAttribute("name", "jimureport");
        response.sendRedirect("/enterprise/index");
    };
}