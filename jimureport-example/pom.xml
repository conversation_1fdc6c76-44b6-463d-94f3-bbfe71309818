<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
        <relativePath/>
    </parent>

    <name>jimureport-example</name>
    <groupId>org.jeecg</groupId>
    <artifactId>jimureport-example</artifactId>
    <version>2.1</version>

    <url>http://www.jimureport.com</url>
    <description>积木报表集成示例</description>

    <repositories>
        <repository>
            <id>aliyun</id>
            <name>aliyun Repository</name>
            <url>https://maven.aliyun.com/nexus/content/groups/public</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>jeecg</id>
            <name>jeecg Repository</name>
            <url>https://maven.jeecg.org/nexus/content/repositories/jeecg</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>jeecg-snapshots</id>
            <name>jeecg snapshots Repository</name>
            <url>https://maven.jeecg.org/nexus/content/repositories/snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <properties>
        <java.version>1.8</java.version>
        <minio.version>8.0.3</minio.version>
        <!-- DB驱动 -->
        <mysql-connector-java.version>8.0.27</mysql-connector-java.version>
        <!-- SQL解析引擎 -->
        <jsqlparser.version>4.6</jsqlparser.version>
    </properties>


    <dependencies>
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!-- 积木报表 -->
        <dependency>
            <groupId>org.jeecgframework.jimureport</groupId>
            <artifactId>jimureport-spring-boot-starter</artifactId>
            <version>2.1.1</version>
        </dependency>
        <!-- mongo、redis和文件数据集支持包，按需引入 -->
        <dependency>
            <groupId>org.jeecgframework.jimureport</groupId>
            <artifactId>jimureport-nosql-starter</artifactId>
            <version>2.0.0</version>
        </dependency>
        <!-- 后台导出接口Echart图表支持包，按需引入 -->
        <dependency>
            <groupId>org.jeecgframework.jimureport</groupId>
            <artifactId>jimureport-echarts-starter</artifactId>
            <version>2.1.1</version>
        </dependency>
        <!-- 积木BI大屏 -->
<!--        <dependency>-->
<!--            <groupId>org.jeecgframework.jimureport</groupId>-->
<!--            <artifactId>jimubi-spring-boot-starter</artifactId>-->
<!--            <version>2.1.0</version>-->
<!--        </dependency>-->
        <!-- jsqlparser -->
        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
            <version>${jsqlparser.version}</version>
        </dependency>


        <!-- SpringBoot-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>

        </dependency>

        <!--spring security-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>

        <!-- 热部署依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
            <optional>true</optional>
        </dependency>

        <!--mybatis-plus-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.5.12</version>
        </dependency>

        <!--分页插件-->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>2.1.0</version>
        </dependency>

        <!-- minio oss-->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>${minio.version}</version>
            <optional>true</optional>
        </dependency>

        <!-- ============================数据库驱动========================== -->
        <!--mysql-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql-connector-java.version}</version>
            <scope>runtime</scope>
        </dependency>
        <!-- oracle驱动-->
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>ojdbc6</artifactId>
            <version>11.2.0.3</version>
            <scope>runtime</scope>
        </dependency>
        <!--  sqlserver-->
       <dependency>
           <groupId>com.microsoft.sqlserver</groupId>
           <artifactId>sqljdbc4</artifactId>
           <version>4.0</version>
           <scope>runtime</scope>
       </dependency>
        <!-- postgresql驱动-->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.2.6</version>
            <scope>runtime</scope>
        </dependency>
        <!-- ===需要什么数据库，手工打开注释=== -->
        <!-- 达梦驱动-->
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>Dm8JdbcDriver18</artifactId>
            <version>8.1.1.49</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmDialect-for-hibernate5.0</artifactId>
            <version>8.1.1.49</version>
            <scope>runtime</scope>
        </dependency>
        <!-- sqlite-->
        <dependency>
            <groupId>org.xerial</groupId>
            <artifactId>sqlite-jdbc</artifactId>
            <version>3.34.0</version>
            <scope>runtime</scope>
        </dependency>
        <!--hsqldb-->
        <dependency>
            <groupId>org.hsqldb</groupId>
            <artifactId>hsqldb</artifactId>
            <version>2.2.8</version>
            <scope>runtime</scope>
        </dependency>
        <!--h2-->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>1.4.197</version>
            <scope>runtime</scope>
        </dependency>
        <!--derby-->
        <dependency>
            <groupId>org.apache.derby</groupId>
            <artifactId>derbyclient</artifactId>
            <version>10.11.1.1</version>
            <scope>runtime</scope>
        </dependency>
        <!--db2-->
        <dependency>
            <groupId>com.ibm.db2</groupId>
            <artifactId>jcc</artifactId>
            <version>11.5.0.0</version>
            <scope>runtime</scope>
        </dependency>
        <!--神通-->
        <dependency>
            <groupId>com.csicit.thirdparty</groupId>
            <artifactId>oscar</artifactId>
            <version>1.0.1</version>
            <scope>runtime</scope>
        </dependency>
        <!--人大金仓-->
        <dependency>
            <groupId>org.jeecgframework</groupId>
            <artifactId>kingbase8</artifactId>
            <version>9.0.0</version>
            <scope>runtime</scope>
        </dependency>
        <!-- ============================数据库驱动========================== -->
    </dependencies>

    <build>
        <plugins>
            <!-- SpringBoot启动 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>dev</profiles.active>
            </properties>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <profiles.active>test</profiles.active>
            </properties>
        </profile>
    </profiles>

</project>
